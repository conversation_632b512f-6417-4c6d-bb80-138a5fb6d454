import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Dimensions,
} from 'react-native';
import { COLORS, CATEGORY_COLORS, DIFFICULTY_COLORS, SHADOWS, SPACING, BORDER_RADIUS, IMAGES } from '../utils/constants';

const { width } = Dimensions.get('window');
const CARD_WIDTH = width * 0.75;

const FeaturedTrekCard = ({ trek, onPress }) => {
  const categoryData = CATEGORY_COLORS[trek.category] || CATEGORY_COLORS.trek;
  const difficultyData = DIFFICULTY_COLORS[trek.difficulty] || DIFFICULTY_COLORS.Easy;
  
  // Get image from local assets
  const getImageSource = () => {
    return IMAGES[trek.imageKey] || IMAGES.defaultImage;
  };

  return (
    <TouchableOpacity 
      style={styles.cardContainer} 
      onPress={() => onPress(trek)}
      activeOpacity={0.8}
    >
      <View style={styles.card}>
        {/* Image Section */}
        <View style={styles.imageContainer}>
          <Image
            source={getImageSource()}
            style={styles.image}
            resizeMode="cover"
          />
          
          {/* Featured Badge */}
          <View style={styles.featuredBadge}>
            <Text style={styles.featuredIcon}>⭐</Text>
            <Text style={styles.featuredText}>Featured</Text>
          </View>
          
          {/* Category Badge */}
          <View style={[styles.categoryBadge, { backgroundColor: categoryData.primary }]}>
            <Text style={styles.categoryIcon}>{categoryData.emoji}</Text>
          </View>
        </View>
        
        {/* Content Section */}
        <View style={styles.content}>
          <Text style={styles.title}>{trek.name}</Text>
          <View style={styles.locationContainer}>
            <Text style={styles.locationIcon}>📍</Text>
            <Text style={styles.location}>{trek.location}</Text>
          </View>
          
          <View style={styles.statsRow}>
            <View style={[styles.difficultyBadge, { backgroundColor: difficultyData.background }]}>
              <Text style={[styles.difficultyText, { color: difficultyData.color }]}>
                {trek.difficulty}
              </Text>
            </View>
            
            <View style={styles.ratingContainer}>
              <Text style={styles.ratingIcon}>⭐</Text>
              <Text style={styles.ratingText}>{trek.rating}</Text>
              <Text style={styles.reviewCount}>({trek.reviewCount})</Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    width: CARD_WIDTH,
    marginRight: SPACING.lg,
  },
  card: {
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: COLORS.surfaceBorder,
    ...SHADOWS.medium,
  },
  
  // Image section
  imageContainer: {
    position: 'relative',
    height: 160,
  },
  image: {
    width: '100%',
    height: '100%',
    backgroundColor: COLORS.backgroundSecondary,
  },
  featuredBadge: {
    position: 'absolute',
    top: SPACING.md,
    left: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.accent,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    ...SHADOWS.small,
  },
  featuredIcon: {
    fontSize: 12,
    marginRight: SPACING.xs,
  },
  featuredText: {
    color: COLORS.textInverse,
    fontSize: 11,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
  categoryBadge: {
    position: 'absolute',
    top: SPACING.md,
    right: SPACING.md,
    width: 36,
    height: 36,
    borderRadius: BORDER_RADIUS.full,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.small,
  },
  categoryIcon: {
    fontSize: 18,
  },
  
  // Content section
  content: {
    padding: SPACING.lg,
  },
  title: {
    fontSize: 16,
    fontWeight: '800',
    color: COLORS.text,
    marginBottom: SPACING.xs,
    lineHeight: 22,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  locationIcon: {
    fontSize: 12,
    marginRight: SPACING.xs,
  },
  location: {
    fontSize: 13,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  
  // Stats section
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  difficultyBadge: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
  },
  difficultyText: {
    fontSize: 11,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingIcon: {
    fontSize: 12,
    marginRight: SPACING.xs,
  },
  ratingText: {
    fontSize: 13,
    fontWeight: '700',
    color: COLORS.text,
    marginRight: SPACING.xs,
  },
  reviewCount: {
    fontSize: 12,
    color: COLORS.textLight,
    fontWeight: '500',
  },
});

export default FeaturedTrekCard;
