import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  StatusBar,
  FlatList,
  ImageBackground,
} from 'react-native';
import FeaturedTrekCard from '../components/FeaturedTrekCard';
import { COLORS, CATEGORIES, CATEGORY_COLORS, SHADOWS, SPACING, BORDER_RADIUS, IMAGES } from '../utils/constants';
import treksData from '../data/treksData.json';

const { width, height } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  const categories = [
    {
      id: CATEGORIES.FORT,
      title: 'Historic Forts',
      description: 'Ancient citadels & royal fortresses',
      ...CATEGORY_COLORS[CATEGORIES.FORT],
      count: '15+',
      backgroundImage: IMAGES.fortBackground,
    },
    {
      id: CATEGORIES.WATERFALL,
      title: 'Waterfalls',
      description: 'Nature\'s spectacular cascades',
      ...CATEGORY_COLORS[CATEGORIES.WATERFALL],
      count: '20+',
      backgroundImage: IMAGES.waterfallBackground,
    },
    {
      id: CATEGORIES.TREK,
      title: 'Adventure Treks',
      description: 'Mountain trails & peak conquests',
      ...CATEGORY_COLORS[CATEGORIES.TREK],
      count: '30+',
      backgroundImage: IMAGES.trekBackground,
    },
  ];

  // Get featured treks
  const featuredTreks = treksData.filter(trek => trek.featured);

  const handleCategoryPress = (category) => {
    navigation.navigate('TrekList', { category: category.id });
  };

  const handleViewAllPress = () => {
    navigation.navigate('TrekList', { category: null });
  };

  const handleTrekPress = (trek) => {
    navigation.navigate('TrekDetails', { trek });
  };

  const renderFeaturedTrek = ({ item }) => (
    <FeaturedTrekCard trek={item} onPress={handleTrekPress} />
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} />

      {/* Header Section */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.greeting}>Welcome to</Text>
          <Text style={styles.title}>Maharashtra Treks</Text>
          <Text style={styles.subtitle}>
            Discover ancient forts, pristine waterfalls, and challenging treks across the Western Ghats
          </Text>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>65+</Text>
            <Text style={styles.statLabel}>Destinations</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>3</Text>
            <Text style={styles.statLabel}>Categories</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>4.8★</Text>
            <Text style={styles.statLabel}>Rating</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Featured Treks Section */}
        <View style={styles.featuredSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Treks</Text>
            <TouchableOpacity onPress={handleViewAllPress}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={featuredTreks}
            renderItem={renderFeaturedTrek}
            keyExtractor={(item) => item.id.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.featuredList}
          />
        </View>

        {/* Categories Section */}
        <View style={styles.categoriesContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Explore Categories</Text>
            <Text style={styles.sectionSubtitle}>Choose your adventure type</Text>
          </View>

          {categories.map((category, index) => (
            <TouchableOpacity
              key={category.id}
              style={[styles.categoryCard, { marginBottom: SPACING.lg }]}
              onPress={() => handleCategoryPress(category)}
              activeOpacity={0.8}
            >
              <ImageBackground
                source={category.backgroundImage}
                style={styles.categoryImageBackground}
                imageStyle={styles.categoryBackgroundImage}
              >
                <View style={styles.categoryOverlay}>
                  <View style={styles.categoryContent}>
                    <View style={styles.categoryLeft}>
                      <View style={styles.categoryIconContainer}>
                        <Text style={styles.categoryIcon}>{category.emoji}</Text>
                      </View>
                      <View style={styles.categoryInfo}>
                        <Text style={styles.categoryTitle}>{category.title}</Text>
                        <Text style={styles.categoryDescription}>{category.description}</Text>
                      </View>
                    </View>

                    <View style={styles.categoryRight}>
                      <Text style={styles.categoryCount}>{category.count}</Text>
                      <View style={styles.arrowContainer}>
                        <Text style={styles.arrowIcon}>→</Text>
                      </View>
                    </View>
                  </View>
                </View>
              </ImageBackground>
            </TouchableOpacity>
          ))}
        </View>

        {/* Quick Action */}
        <View style={styles.quickActionContainer}>
          <TouchableOpacity
            style={[styles.viewAllButton, { backgroundColor: COLORS.primary }]}
            onPress={handleViewAllPress}
            activeOpacity={0.8}
          >
            <Text style={styles.viewAllIcon}>🗺️</Text>
            <View style={styles.viewAllTextContainer}>
              <Text style={styles.viewAllText}>Explore All Destinations</Text>
              <Text style={styles.viewAllSubtext}>65+ amazing places to discover</Text>
            </View>
            <Text style={styles.viewAllArrow}>→</Text>
          </TouchableOpacity>
        </View>

        {/* Features Section */}
        <View style={styles.featuresSection}>
          <View style={styles.featuresHeader}>
            <Text style={styles.featuresTitle}>Why Choose Our App?</Text>
            <Text style={styles.featuresSubtitle}>Everything you need for the perfect adventure</Text>
          </View>

          <View style={styles.featuresGrid}>
            {[
              {
                icon: '📍',
                title: 'Precise Locations',
                desc: 'GPS coordinates & detailed directions',
                color: COLORS.info
              },
              {
                icon: '🗺️',
                title: 'Offline Maps',
                desc: 'Navigate without internet connection',
                color: COLORS.success
              },
              {
                icon: '📞',
                title: 'Local Contacts',
                desc: 'Verified guides & emergency numbers',
                color: COLORS.warning
              },
              {
                icon: '⏱️',
                title: 'Detailed Info',
                desc: 'Duration, difficulty & best times',
                color: COLORS.error
              },
            ].map((feature, index) => (
              <View key={index} style={styles.featureCard}>
                <View style={[styles.featureIconContainer, { backgroundColor: feature.color }]}>
                  <Text style={styles.featureIcon}>{feature.icon}</Text>
                </View>
                <Text style={styles.featureTitle}>{feature.title}</Text>
                <Text style={styles.featureDesc}>{feature.desc}</Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },

  // Header Section
  header: {
    backgroundColor: COLORS.backgroundCard,
    paddingTop: SPACING.lg,
    paddingBottom: SPACING.xl,
    paddingHorizontal: SPACING.xl,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceBorder,
  },
  headerContent: {
    marginBottom: SPACING.lg,
  },
  greeting: {
    fontSize: 16,
    color: COLORS.textSecondary,
    fontWeight: '500',
    marginBottom: SPACING.xs,
  },
  title: {
    fontSize: 28,
    fontWeight: '900',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    lineHeight: 24,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statCard: {
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: BORDER_RADIUS.lg,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    alignItems: 'center',
    minWidth: 80,
    ...SHADOWS.small,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '800',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.textSecondary,
    fontWeight: '600',
    textAlign: 'center',
  },

  // Scroll View
  scrollView: {
    flex: 1,
    backgroundColor: COLORS.background,
  },

  // Featured Section
  featuredSection: {
    paddingTop: SPACING.xl,
    paddingBottom: SPACING.lg,
  },
  featuredList: {
    paddingLeft: SPACING.xl,
  },

  // Categories Section
  categoriesContainer: {
    padding: SPACING.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
    paddingHorizontal: SPACING.xl,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '800',
    color: COLORS.text,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
    marginTop: SPACING.xs,
  },
  seeAllText: {
    fontSize: 16,
    color: COLORS.primary,
    fontWeight: '700',
  },
  categoryCard: {
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    ...SHADOWS.medium,
  },
  categoryImageBackground: {
    minHeight: 120,
  },
  categoryBackgroundImage: {
    borderRadius: BORDER_RADIUS.lg,
  },
  categoryOverlay: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: SPACING.lg,
  },
  categoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIconContainer: {
    width: 56,
    height: 56,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.lg,
  },
  categoryIcon: {
    fontSize: 24,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: '800',
    color: COLORS.textInverse,
    marginBottom: SPACING.xs,
  },
  categoryDescription: {
    fontSize: 14,
    color: COLORS.textInverse,
    fontWeight: '500',
    lineHeight: 20,
    opacity: 0.9,
  },
  categoryRight: {
    alignItems: 'flex-end',
  },
  categoryCount: {
    fontSize: 16,
    fontWeight: '800',
    color: COLORS.textInverse,
    marginBottom: SPACING.sm,
  },
  arrowContainer: {
    width: 32,
    height: 32,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowIcon: {
    fontSize: 16,
    color: COLORS.textInverse,
    fontWeight: '700',
  },

  // Quick Action Section
  quickActionContainer: {
    paddingHorizontal: SPACING.xl,
    marginBottom: SPACING.xl,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.xl,
    borderRadius: BORDER_RADIUS.lg,
    ...SHADOWS.medium,
  },
  viewAllIcon: {
    fontSize: 24,
    marginRight: SPACING.lg,
  },
  viewAllTextContainer: {
    flex: 1,
  },
  viewAllText: {
    color: COLORS.textInverse,
    fontSize: 18,
    fontWeight: '800',
    marginBottom: SPACING.xs,
  },
  viewAllSubtext: {
    color: COLORS.textInverse,
    fontSize: 14,
    fontWeight: '500',
    opacity: 0.9,
  },
  viewAllArrow: {
    fontSize: 20,
    color: COLORS.textInverse,
    fontWeight: '700',
  },

  // Features Section
  featuresSection: {
    padding: SPACING.xl,
    backgroundColor: COLORS.backgroundCard,
    marginTop: SPACING.md,
  },
  featuresHeader: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  featuresTitle: {
    fontSize: 22,
    fontWeight: '800',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  featuresSubtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
    fontWeight: '500',
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featureCard: {
    width: (width - SPACING.xl * 3) / 2,
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.surfaceBorder,
    ...SHADOWS.small,
  },
  featureIconContainer: {
    width: 56,
    height: 56,
    borderRadius: BORDER_RADIUS.full,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  featureIcon: {
    fontSize: 24,
    color: COLORS.textInverse,
  },
  featureTitle: {
    fontSize: 14,
    fontWeight: '700',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  featureDesc: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
    fontWeight: '500',
  },
});

export default HomeScreen;
