import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  StatusBar,
  FlatList,
  Image,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS, CATEGORIES, CATEGORY_COLORS, SHADOWS, SPACING, BORDER_RADIUS, IMAGES } from '../utils/constants';
import treksData from '../data/treksData.json';

const { width, height } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  const [searchText, setSearchText] = useState('');

  // Get featured treks (Top DIY Treks)
  const topTreks = treksData.filter(trek => trek.featured);

  // Popular nearby treks (circular images)
  const popularNearby = treksData.slice(0, 4).map(trek => ({
    ...trek,
    shortName: trek.name.split(' ')[0], // First word for display
  }));

  const handleTrekPress = (trek) => {
    navigation.navigate('TrekDetails', { trek });
  };

  const handleViewAllPress = () => {
    navigation.navigate('TrekList', { category: null });
  };

  const handleSearchPress = () => {
    // Navigate to search screen or show search results
    navigation.navigate('TrekList', { searchQuery: searchText });
  };

  const getImageSource = (trek) => {
    return IMAGES[trek.imageKey] || IMAGES.defaultImage;
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return COLORS.success;
      case 'moderate': return COLORS.warning;
      case 'difficult': return COLORS.error;
      default: return COLORS.textSecondary;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} />

      {/* Professional Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <View style={styles.brandContainer}>
              <View style={styles.logoContainer}>
                <Text style={styles.logoIcon}>🏔️</Text>
              </View>
              <View style={styles.brandText}>
                <Text style={styles.appName}>TrekMate</Text>
                <Text style={styles.tagline}>Maharashtra Adventures</Text>
              </View>
            </View>
            <TouchableOpacity style={styles.profileButton}>
              <Image
                source={IMAGES.defaultImage}
                style={styles.profileImage}
              />
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationText}>3</Text>
              </View>
            </TouchableOpacity>
          </View>

          {/* Enhanced Search Bar */}
          <View style={styles.searchContainer}>
            <View style={styles.searchBar}>
              <View style={styles.searchIconContainer}>
                <Text style={styles.searchIcon}>🔍</Text>
              </View>
              <TextInput
                style={styles.searchInput}
                placeholder="Search destinations, guides, gear..."
                placeholderTextColor={COLORS.textLight}
                value={searchText}
                onChangeText={setSearchText}
                onSubmitEditing={handleSearchPress}
              />
              <TouchableOpacity style={styles.filterButton}>
                <Text style={styles.filterIcon}>⚙️</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Quick Stats Banner */}
        <View style={styles.statsSection}>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>65+</Text>
              <Text style={styles.statLabel}>Destinations</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>4.8★</Text>
              <Text style={styles.statLabel}>Avg Rating</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>12K+</Text>
              <Text style={styles.statLabel}>Happy Trekkers</Text>
            </View>
          </View>
        </View>

        {/* Featured Destinations */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View>
              <Text style={styles.sectionTitle}>Featured Destinations</Text>
              <Text style={styles.sectionSubtitle}>Handpicked adventures for you</Text>
            </View>
            <TouchableOpacity style={styles.viewAllButton} onPress={handleViewAllPress}>
              <Text style={styles.viewAllText}>View All</Text>
              <Text style={styles.viewAllArrow}>→</Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={topTreks}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.featuredCard}
                onPress={() => handleTrekPress(item)}
                activeOpacity={0.95}
              >
                <View style={styles.cardImageContainer}>
                  <Image
                    source={getImageSource(item)}
                    style={styles.cardImage}
                  />
                  <LinearGradient
                    colors={['transparent', 'rgba(0,0,0,0.7)']}
                    style={styles.cardGradient}
                  />
                  <View style={styles.cardBadges}>
                    <View style={styles.ratingBadge}>
                      <Text style={styles.ratingIcon}>⭐</Text>
                      <Text style={styles.ratingText}>{item.rating}</Text>
                    </View>
                    <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(item.difficulty) }]}>
                      <Text style={styles.difficultyText}>{item.difficulty}</Text>
                    </View>
                  </View>
                </View>
                <View style={styles.cardContent}>
                  <Text style={styles.cardTitle}>{item.name}</Text>
                  <View style={styles.cardMeta}>
                    <Text style={styles.cardLocation}>📍 {item.location}</Text>
                    <Text style={styles.cardDuration}>⏱️ {item.duration}</Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}
            keyExtractor={(item) => item.id.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.featuredList}
          />
        </View>

        {/* Categories Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View>
              <Text style={styles.sectionTitle}>Explore Categories</Text>
              <Text style={styles.sectionSubtitle}>Choose your adventure type</Text>
            </View>
          </View>

          <View style={styles.categoriesGrid}>
            {[
              { id: 'forts', title: 'Historic Forts', icon: '🏰', count: '15+', color: COLORS.fort },
              { id: 'waterfalls', title: 'Waterfalls', icon: '💧', count: '20+', color: COLORS.waterfall },
              { id: 'treks', title: 'Mountain Treks', icon: '⛰️', count: '30+', color: COLORS.trek },
            ].map((category, index) => (
              <TouchableOpacity
                key={category.id}
                style={styles.categoryCard}
                onPress={() => navigation.navigate('TrekList', { category: category.id })}
                activeOpacity={0.9}
              >
                <View style={[styles.categoryIcon, { backgroundColor: category.color + '15' }]}>
                  <Text style={styles.categoryEmoji}>{category.icon}</Text>
                </View>
                <Text style={styles.categoryTitle}>{category.title}</Text>
                <Text style={styles.categoryCount}>{category.count} destinations</Text>
                <View style={[styles.categoryAccent, { backgroundColor: category.color }]} />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Popular Destinations */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View>
              <Text style={styles.sectionTitle}>Popular Destinations</Text>
              <Text style={styles.sectionSubtitle}>Trending this month</Text>
            </View>
            <TouchableOpacity style={styles.viewAllButton} onPress={handleViewAllPress}>
              <Text style={styles.viewAllText}>View All</Text>
              <Text style={styles.viewAllArrow}>→</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.popularGrid}>
            {popularNearby.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={styles.popularCard}
                onPress={() => handleTrekPress(item)}
                activeOpacity={0.9}
              >
                <Image
                  source={getImageSource(item)}
                  style={styles.popularImage}
                />
                <View style={styles.popularOverlay}>
                  <Text style={styles.popularName}>{item.shortName}</Text>
                  <Text style={styles.popularRating}>⭐ {item.rating}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Professional CTA Section */}
        <View style={styles.ctaSection}>
          <LinearGradient
            colors={[COLORS.primary, COLORS.primaryLight]}
            style={styles.ctaGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.ctaContent}>
              <Text style={styles.ctaTitle}>Ready for Adventure?</Text>
              <Text style={styles.ctaSubtitle}>
                Join 12K+ trekkers exploring Maharashtra's hidden gems
              </Text>
              <TouchableOpacity style={styles.ctaButton} onPress={handleViewAllPress}>
                <Text style={styles.ctaButtonText}>Explore All Destinations</Text>
                <Text style={styles.ctaButtonArrow}>→</Text>
              </TouchableOpacity>
            </View>
          </LinearGradient>
        </View>

        {/* Quick Tools Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View>
              <Text style={styles.sectionTitle}>Quick Tools</Text>
              <Text style={styles.sectionSubtitle}>Everything you need for trekking</Text>
            </View>
          </View>

          <View style={styles.toolsGrid}>
            {[
              { icon: '🗺️', title: 'Offline Maps', desc: 'Download for offline use', color: COLORS.info },
              { icon: '📞', title: 'Emergency Contacts', desc: 'Local guides & rescue', color: COLORS.error },
              { icon: '🎒', title: 'Packing Lists', desc: 'Never forget essentials', color: COLORS.warning },
              { icon: '🌤️', title: 'Weather Updates', desc: 'Real-time conditions', color: COLORS.success },
            ].map((tool, index) => (
              <TouchableOpacity key={index} style={styles.toolCard} activeOpacity={0.9}>
                <View style={[styles.toolIcon, { backgroundColor: tool.color + '15' }]}>
                  <Text style={styles.toolEmoji}>{tool.icon}</Text>
                </View>
                <Text style={styles.toolTitle}>{tool.title}</Text>
                <Text style={styles.toolDesc}>{tool.desc}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },

  // Professional Header Styles
  header: {
    backgroundColor: COLORS.backgroundCard,
    paddingTop: SPACING.md,
    paddingBottom: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceBorder,
    ...SHADOWS.small,
  },
  headerContent: {
    paddingHorizontal: SPACING.xl,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  brandContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  logoIcon: {
    fontSize: 24,
  },
  brandText: {
    flex: 1,
  },
  appName: {
    fontSize: 24,
    fontWeight: '900',
    color: COLORS.text,
    letterSpacing: -0.5,
  },
  tagline: {
    fontSize: 13,
    color: COLORS.textSecondary,
    fontWeight: '600',
    marginTop: 2,
  },
  profileButton: {
    position: 'relative',
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: COLORS.primary + '20',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  notificationBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: COLORS.error,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.backgroundCard,
  },
  notificationText: {
    fontSize: 10,
    fontWeight: '700',
    color: COLORS.textInverse,
  },

  // Enhanced Search Styles
  searchContainer: {
    marginTop: SPACING.md,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: BORDER_RADIUS.xl,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.surfaceBorder,
  },
  searchIconContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm,
  },
  searchIcon: {
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
    paddingVertical: 0,
  },
  filterButton: {
    width: 32,
    height: 32,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: SPACING.sm,
  },
  filterIcon: {
    fontSize: 14,
    color: COLORS.primary,
  },

  // Content Styles
  scrollView: {
    flex: 1,
  },

  // Stats Section
  statsSection: {
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.lg,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    ...SHADOWS.medium,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: COLORS.surfaceBorder,
    marginHorizontal: SPACING.md,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '900',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.textSecondary,
    textAlign: 'center',
  },

  // Section Styles
  section: {
    paddingHorizontal: SPACING.xl,
    marginBottom: SPACING.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '900',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary + '10',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    borderWidth: 1,
    borderColor: COLORS.primary + '20',
  },
  viewAllText: {
    fontSize: 13,
    fontWeight: '700',
    color: COLORS.primary,
    marginRight: SPACING.xs,
  },
  viewAllArrow: {
    fontSize: 12,
    fontWeight: '700',
    color: COLORS.primary,
  },

  // Featured Cards Styles
  featuredList: {
    paddingLeft: SPACING.xl,
  },
  featuredCard: {
    width: width * 0.75,
    marginRight: SPACING.lg,
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
    ...SHADOWS.large,
  },
  cardImageContainer: {
    position: 'relative',
    height: 200,
  },
  cardImage: {
    width: '100%',
    height: '100%',
    backgroundColor: COLORS.backgroundSecondary,
  },
  cardGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
  },
  cardBadges: {
    position: 'absolute',
    top: SPACING.md,
    left: SPACING.md,
    right: SPACING.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
  },
  ratingIcon: {
    fontSize: 12,
    marginRight: SPACING.xs,
  },
  ratingText: {
    fontSize: 13,
    fontWeight: '700',
    color: COLORS.textInverse,
  },
  difficultyBadge: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
  },
  difficultyText: {
    fontSize: 11,
    fontWeight: '800',
    color: COLORS.textInverse,
    textTransform: 'uppercase',
  },
  cardContent: {
    padding: SPACING.lg,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '900',
    color: COLORS.text,
    marginBottom: SPACING.md,
    lineHeight: 24,
  },
  cardMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardLocation: {
    fontSize: 13,
    color: COLORS.textSecondary,
    fontWeight: '600',
  },
  cardDuration: {
    fontSize: 13,
    color: COLORS.textSecondary,
    fontWeight: '600',
  },

  // Categories Grid Styles
  categoriesGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  categoryCard: {
    flex: 1,
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    marginHorizontal: SPACING.xs,
    alignItems: 'center',
    position: 'relative',
    borderWidth: 1,
    borderColor: COLORS.surfaceBorder,
    ...SHADOWS.medium,
  },
  categoryIcon: {
    width: 56,
    height: 56,
    borderRadius: BORDER_RADIUS.xl,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  categoryEmoji: {
    fontSize: 24,
  },
  categoryTitle: {
    fontSize: 14,
    fontWeight: '800',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  categoryCount: {
    fontSize: 11,
    color: COLORS.textSecondary,
    fontWeight: '600',
    textAlign: 'center',
  },
  categoryAccent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 3,
    borderBottomLeftRadius: BORDER_RADIUS.xl,
    borderBottomRightRadius: BORDER_RADIUS.xl,
  },

  // Popular Grid Styles
  popularGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  popularCard: {
    flex: 1,
    marginHorizontal: SPACING.xs,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    ...SHADOWS.medium,
  },
  popularImage: {
    width: '100%',
    height: 80,
    backgroundColor: COLORS.backgroundSecondary,
  },
  popularOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: SPACING.sm,
  },
  popularName: {
    fontSize: 12,
    fontWeight: '700',
    color: COLORS.textInverse,
    marginBottom: SPACING.xs,
  },
  popularRating: {
    fontSize: 10,
    color: COLORS.textInverse,
    fontWeight: '600',
  },

  // CTA Section Styles
  ctaSection: {
    marginHorizontal: SPACING.xl,
    marginBottom: SPACING.xl,
    borderRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
    ...SHADOWS.large,
  },
  ctaGradient: {
    padding: SPACING.xl,
  },
  ctaContent: {
    alignItems: 'center',
  },
  ctaTitle: {
    fontSize: 24,
    fontWeight: '900',
    color: COLORS.textInverse,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  ctaSubtitle: {
    fontSize: 16,
    color: COLORS.textInverse,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    opacity: 0.9,
    lineHeight: 22,
  },
  ctaButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.textInverse,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.lg,
    borderRadius: BORDER_RADIUS.full,
    ...SHADOWS.medium,
  },
  ctaButtonText: {
    fontSize: 16,
    fontWeight: '800',
    color: COLORS.primary,
    marginRight: SPACING.sm,
  },
  ctaButtonArrow: {
    fontSize: 16,
    fontWeight: '800',
    color: COLORS.primary,
  },

  // Tools Grid Styles
  toolsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  toolCard: {
    width: (width - SPACING.xl * 3) / 2,
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.surfaceBorder,
    ...SHADOWS.medium,
  },
  toolIcon: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  toolEmoji: {
    fontSize: 20,
  },
  toolTitle: {
    fontSize: 14,
    fontWeight: '800',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  toolDesc: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 16,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: SPACING.xl,
  },
});

export default HomeScreen;
