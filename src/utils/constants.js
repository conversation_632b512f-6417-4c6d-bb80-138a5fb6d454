export const CATEGORIES = {
  FORT: 'fort',
  WATERFALL: 'waterfall',
  TREK: 'trek',
};

export const DIFFICULTY_LEVELS = {
  EASY: 'Easy',
  MODERATE: 'Moderate',
  DIFFICULT: 'Difficult',
};

export const COLORS = {
  // Modern brand colors - Professional and clean
  primary: '#2563EB',
  primaryLight: '#3B82F6',
  primaryDark: '#1D4ED8',

  // Secondary colors - Complementary
  secondary: '#059669',
  secondaryLight: '#10B981',
  secondaryDark: '#047857',

  // Accent colors - Vibrant highlights
  accent: '#F59E0B',
  accentLight: '#FBBF24',
  accentDark: '#D97706',

  // Background colors - Clean and modern
  background: '#F8FAFC',
  backgroundSecondary: '#F1F5F9',
  backgroundCard: '#FFFFFF',

  // Surface colors
  surface: '#FFFFFF',
  surfaceElevated: '#FFFFFF',
  surfaceBorder: '#E2E8F0',

  // Text colors - High contrast
  text: '#0F172A',
  textSecondary: '#475569',
  textLight: '#94A3B8',
  textInverse: '#FFFFFF',

  // Status colors - Clear and accessible
  success: '#059669',
  warning: '#F59E0B',
  error: '#DC2626',
  info: '#2563EB',

  // Category colors - Vibrant and distinct
  fort: '#7C2D12',
  waterfall: '#1E40AF',
  trek: '#059669',

  // Difficulty colors
  easy: '#059669',
  moderate: '#F59E0B',
  difficult: '#DC2626',

  // Shadow colors - Subtle depth
  shadow: {
    light: 'rgba(0, 0, 0, 0.04)',
    medium: 'rgba(0, 0, 0, 0.08)',
    dark: 'rgba(0, 0, 0, 0.12)',
  },
};

export const CATEGORY_COLORS = {
  [CATEGORIES.FORT]: {
    primary: COLORS.fort,
    background: 'rgba(139, 69, 19, 0.1)',
    icon: '🏰',
    emoji: '🏛️',
    theme: 'heritage',
  },
  [CATEGORIES.WATERFALL]: {
    primary: COLORS.waterfall,
    background: 'rgba(30, 64, 175, 0.1)',
    icon: '💧',
    emoji: '🌊',
    theme: 'water',
  },
  [CATEGORIES.TREK]: {
    primary: COLORS.trek,
    background: 'rgba(5, 150, 105, 0.1)',
    icon: '⛰️',
    emoji: '🥾',
    theme: 'adventure',
  },
};

export const DIFFICULTY_COLORS = {
  [DIFFICULTY_LEVELS.EASY]: {
    color: COLORS.easy,
    background: '#ECFDF5',
    icon: '🟢',
    emoji: '😊',
    label: 'Beginner Friendly',
  },
  [DIFFICULTY_LEVELS.MODERATE]: {
    color: COLORS.moderate,
    background: '#FFFBEB',
    icon: '🟡',
    emoji: '💪',
    label: 'Moderate Challenge',
  },
  [DIFFICULTY_LEVELS.DIFFICULT]: {
    color: COLORS.difficult,
    background: '#FEF2F2',
    icon: '🔴',
    emoji: '🔥',
    label: 'Expert Level',
  },
};

export const SHADOWS = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  small: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  medium: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  large: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  xl: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
};

export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const BORDER_RADIUS = {
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  full: 9999,
};

// Import font configuration
export { FONTS, FONT_WEIGHTS, TYPOGRAPHY, getFontStyle, createTextStyle } from './fonts';

export const IMAGES = {
  // Trek images from assets/img folder
  rajgad: require('../../assets/img/rajgad.jpg'),
  dudhsagar: require('../../assets/img/waterfall.jpg'), // Using waterfall image for Dudhsagar
  harishchandragad: require('../../assets/img/harishchandra.png'),
  sinhagad: require('../../assets/img/raigad.jpg'), // Using raigad for sinhagad
  bhimashankar: require('../../assets/img/harihar.jpg'), // Using harihar for bhimashankar

  // Category background images
  fortBackground: require('../../assets/img/rajgad.jpg'),
  waterfallBackground: require('../../assets/img/waterfall.jpg'),
  trekBackground: require('../../assets/img/harishchandra.png'),

  // Default fallback
  defaultImage: require('../../assets/icon.png'),
};
