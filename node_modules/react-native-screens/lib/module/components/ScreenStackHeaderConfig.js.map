{"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "Image", "Platform", "StyleSheet", "ScreenStackHeaderConfigNativeComponent", "ScreenStackHeaderSubviewNativeComponent", "EDGE_TO_EDGE", "ScreenStackHeaderSubview", "ScreenStackHeaderConfig", "forwardRef", "props", "ref", "createElement", "topInsetEnabled", "style", "styles", "headerConfig", "pointerEvents", "displayName", "ScreenStackHeaderBackButtonImage", "type", "headerSubview", "resizeMode", "fadeDuration", "ScreenStackHeaderRightView", "rest", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "headerSubviewCenter", "ScreenStackHeaderSearchBarView", "create", "flexDirection", "alignItems", "justifyContent", "flexShrink", "position", "width", "OS", "undefined"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackHeaderConfig.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,CAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAL,CAAA,CAAAK,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAL,CAAA,KAAAJ,QAAA,CAAAY,KAAA,OAAAN,SAAA;AAEb,OAAOO,KAAK,MAAM,OAAO;AAEzB,SACEC,KAAK,EAELC,QAAQ,EACRC,UAAU,QAGL,cAAc;;AAErB;AACA,OAAOC,sCAAsC,MAAM,kDAAkD;AACrG,OAAOC,uCAAuC,MAEvC,mDAAmD;AAC1D,SAASC,YAAY,QAAQ,wBAAwB;AAErD,OAAO,MAAMC,wBAAkF,GAC7FF,uCAAuC;AAEzC,OAAO,MAAMG,uBAAuB,gBAAGR,KAAK,CAACS,UAAU,CAGrD,CAACC,KAAK,EAAEC,GAAG,kBACXX,KAAA,CAAAY,aAAA,CAACR,sCAAsC,EAAAjB,QAAA,KACjCuB,KAAK;EACTC,GAAG,EAAEA,GAAI;EACTE,eAAe,EAAEP,YAAY,GAAG,IAAI,GAAGI,KAAK,CAACG,eAAgB;EAC7DC,KAAK,EAAEC,MAAM,CAACC,YAAa;EAC3BC,aAAa,EAAC;AAAU,EACzB,CACF,CAAC;AAEFT,uBAAuB,CAACU,WAAW,GAAG,yBAAyB;AAE/D,OAAO,MAAMC,gCAAgC,GAC3CT,KAAiB,iBAEjBV,KAAA,CAAAY,aAAA,CAACL,wBAAwB;EAACa,IAAI,EAAC,MAAM;EAACN,KAAK,EAAEC,MAAM,CAACM;AAAc,gBAChErB,KAAA,CAAAY,aAAA,CAACX,KAAK,EAAAd,QAAA;EAACmC,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKb,KAAK,CAAG,CAChC,CAC3B;AAED,OAAO,MAAMc,0BAA0B,GAAId,KAAgB,IAAkB;EAC3E,MAAM;IAAEI,KAAK;IAAE,GAAGW;EAAK,CAAC,GAAGf,KAAK;EAEhC,oBACEV,KAAA,CAAAY,aAAA,CAACL,wBAAwB,EAAApB,QAAA,KACnBsC,IAAI;IACRL,IAAI,EAAC,OAAO;IACZN,KAAK,EAAE,CAACC,MAAM,CAACM,aAAa,EAAEP,KAAK;EAAE,EACtC,CAAC;AAEN,CAAC;AAED,OAAO,MAAMY,yBAAyB,GAAIhB,KAAgB,IAAkB;EAC1E,MAAM;IAAEI,KAAK;IAAE,GAAGW;EAAK,CAAC,GAAGf,KAAK;EAEhC,oBACEV,KAAA,CAAAY,aAAA,CAACL,wBAAwB,EAAApB,QAAA,KACnBsC,IAAI;IACRL,IAAI,EAAC,MAAM;IACXN,KAAK,EAAE,CAACC,MAAM,CAACM,aAAa,EAAEP,KAAK;EAAE,EACtC,CAAC;AAEN,CAAC;AAED,OAAO,MAAMa,2BAA2B,GAAIjB,KAAgB,IAAkB;EAC5E,MAAM;IAAEI,KAAK;IAAE,GAAGW;EAAK,CAAC,GAAGf,KAAK;EAEhC,oBACEV,KAAA,CAAAY,aAAA,CAACL,wBAAwB,EAAApB,QAAA,KACnBsC,IAAI;IACRL,IAAI,EAAC,QAAQ;IACbN,KAAK,EAAE,CAACC,MAAM,CAACa,mBAAmB,EAAEd,KAAK;EAAE,EAC5C,CAAC;AAEN,CAAC;AAED,OAAO,MAAMe,8BAA8B,GACzCnB,KAAgB,iBAEhBV,KAAA,CAAAY,aAAA,CAACL,wBAAwB,EAAApB,QAAA,KACnBuB,KAAK;EACTU,IAAI,EAAC,WAAW;EAChBN,KAAK,EAAEC,MAAM,CAACM;AAAc,EAC7B,CACF;AAED,MAAMN,MAAM,GAAGZ,UAAU,CAAC2B,MAAM,CAAC;EAC/BT,aAAa,EAAE;IACbU,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDL,mBAAmB,EAAE;IACnBG,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDlB,YAAY,EAAE;IACZmB,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,MAAM;IACbL,aAAa,EAAE,KAAK;IACpBE,cAAc,EAAE,eAAe;IAC/B;IACA;IACAD,UAAU,EAAE9B,QAAQ,CAACmC,EAAE,KAAK,KAAK,GAAG,QAAQ,GAAGC;EACjD;AACF,CAAC,CAAC", "ignoreList": []}