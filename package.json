{"name": "maharashtra-trek", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "expo": "~53.0.9", "expo-font": "^13.3.1", "expo-linear-gradient": "~14.1.4", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}